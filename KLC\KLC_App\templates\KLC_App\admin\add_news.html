{% extends 'KLC_App/admin/admin_base.html' %}

{% block title %}إضافة خبر جديد - لوحة التحكم{% endblock %}

{% block body_class %}admin-news{% endblock %}

{% block content %}
<div class="section-header d-flex justify-content-between align-items-center mb-4">
    <h2><i class="fas fa-newspaper me-2"></i> إضافة خبر جديد</h2>
    <a href="{% url 'admin_news' %}" class="btn btn-outline-primary">
        <i class="fas fa-arrow-right me-2"></i> العودة إلى قائمة الأخبار
    </a>
</div>

<div class="row">
    <div class="col-lg-8 mx-auto">
        <div class="card shadow-sm">
            <div class="card-body">
                {% if messages %}
                    {% for message in messages %}
                        <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
                            {{ message }}
                            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                        </div>
                    {% endfor %}
                {% endif %}

                <form method="POST" enctype="multipart/form-data" class="admin-form">
                    {% csrf_token %}

                    <div class="mb-4">
                        <h3 class="mb-3 text-primary">معلومات الخبر الجديد</h3>
                        <p class="text-muted mb-4">يرجى إدخال بيانات الخبر بدقة</p>
                    </div>

                    <div class="alert alert-info mb-4">
                        <i class="fas fa-info-circle me-2"></i>
                        جميع الحقول المميزة بعلامة <span class="text-danger">*</span> هي حقول إلزامية.
                    </div>

                    <div class="mb-3">
                        <label for="{{ form.title.id_for_label }}" class="form-label">{{ form.title.label }} <span class="text-danger">*</span></label>
                        {{ form.title }}
                        {% if form.title.errors %}
                            <div class="text-danger mt-1">{{ form.title.errors }}</div>
                        {% endif %}
                    </div>

                    <div class="mb-3">
                        <label for="{{ form.description.id_for_label }}" class="form-label">{{ form.description.label }} <span class="text-danger">*</span></label>
                        {{ form.description }}
                        {% if form.description.errors %}
                            <div class="text-danger mt-1">{{ form.description.errors }}</div>
                        {% endif %}
                    </div>

                    <div class="mb-3">
                        <label for="{{ form.image.id_for_label }}" class="form-label">{{ form.image.label }} <span class="text-danger">*</span></label>
                        {{ form.image }}
                        <div class="form-text">يفضل استخدام صورة بأبعاد 16:9 للحصول على أفضل عرض</div>
                        {% if form.image.errors %}
                            <div class="text-danger mt-1">{{ form.image.errors }}</div>
                        {% endif %}
                    </div>

                    <div class="mb-3">
                        <label for="{{ form.additional_images.id_for_label }}" class="form-label">{{ form.additional_images.label }}</label>
                        {{ form.additional_images }}
                        <div class="form-text">{{ form.additional_images.help_text }}</div>
                        {% if form.additional_images.errors %}
                            <div class="text-danger mt-1">{{ form.additional_images.errors }}</div>
                        {% endif %}
                    </div>

                    <div class="mb-3">
                        <label for="{{ form.video_url.id_for_label }}" class="form-label">{{ form.video_url.label }}</label>
                        {{ form.video_url }}
                        <div class="form-text">يمكنك إضافة رابط فيديو من Google Drive (اختياري)</div>
                        {% if form.video_url.errors %}
                            <div class="text-danger mt-1">{{ form.video_url.errors }}</div>
                        {% endif %}
                    </div>

                    <div class="mb-3">
                        <label for="{{ form.news_type.id_for_label }}" class="form-label">{{ form.news_type.label }}</label>
                        {{ form.news_type }}
                        <div class="form-text">مثال: أخبار، إنجازات، مشاريع، فعاليات</div>
                        {% if form.news_type.errors %}
                            <div class="text-danger mt-1">{{ form.news_type.errors }}</div>
                        {% endif %}
                    </div>

                    <div class="mb-4">
                        <div class="featured-news-option">
                            <div class="card border-0 shadow-sm">
                                <div class="card-body">
                                    <div class="d-flex align-items-center">
                                        <div class="featured-icon me-3">
                                            <i class="fas fa-star"></i>
                                        </div>
                                        <div class="featured-content">
                                            <h5 class="mb-1">خيارات العرض</h5>
                                            <div class="form-check form-switch">
                                                {{ form.is_featured }}
                                                <label class="form-check-label fw-bold" for="{{ form.is_featured.id_for_label }}">
                                                    {{ form.is_featured.label }}
                                                </label>
                                            </div>
                                            <div class="form-text text-muted mt-1">
                                                <i class="fas fa-info-circle me-1"></i>
                                                {{ form.is_featured.help_text }}
                                            </div>
                                            {% if form.is_featured.errors %}
                                                <div class="text-danger mt-2">
                                                    <i class="fas fa-exclamation-circle me-1"></i>
                                                    {{ form.is_featured.errors }}
                                                </div>
                                            {% endif %}
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <style>
                        .featured-news-option .card {
                            border-radius: 10px;
                            transition: all 0.3s ease;
                        }

                        .featured-news-option .card:hover {
                            transform: translateY(-3px);
                            box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1) !important;
                        }

                        .featured-icon {
                            width: 48px;
                            height: 48px;
                            background-color: #fff3cd;
                            color: #ffc107;
                            border-radius: 12px;
                            display: flex;
                            align-items: center;
                            justify-content: center;
                            font-size: 1.5rem;
                        }

                        [data-theme="dark"] .featured-icon {
                            background-color: rgba(255, 193, 7, 0.2);
                        }

                        .form-switch .form-check-input {
                            width: 3em;
                            height: 1.5em;
                            margin-top: 0.25em;
                            cursor: pointer;
                        }

                        .form-switch .form-check-input:checked {
                            background-color: #198754;
                            border-color: #198754;
                        }

                        .form-switch .form-check-label {
                            cursor: pointer;
                            padding-top: 0.25em;
                        }
                    </style>

                    <div class="d-grid gap-2 d-md-flex justify-content-md-end mt-4">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-2"></i> حفظ الخبر
                        </button>
                        <a href="{% url 'admin_news' %}" class="btn btn-outline-secondary">
                            <i class="fas fa-times me-2"></i> إلغاء
                        </a>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // Set the active page for the sidebar
    document.addEventListener('DOMContentLoaded', function() {
        // Add active class to news link
        const newsLink = document.querySelector('a[href="{% url "admin_news" %}"]');
        if (newsLink) {
            newsLink.classList.add('active');
        }

        // Handle featured checkbox
        const featuredCheckbox = document.getElementById('{{ form.is_featured.id_for_label }}');
        if (featuredCheckbox) {
            featuredCheckbox.addEventListener('change', function() {
                if (this.checked) {
                    // Show confirmation dialog
                    if (confirm('تحديد هذا الخبر كخبر رئيسي سيؤدي إلى إلغاء تحديد أي خبر آخر كخبر رئيسي. هل تريد المتابعة؟')) {
                        // User confirmed
                    } else {
                        // User canceled
                        this.checked = false;
                    }
                }
            });
        }

        // Preview additional images when selected
        const additionalImagesInput = document.getElementById('{{ form.additional_images.id_for_label }}');
        if (additionalImagesInput) {
            additionalImagesInput.addEventListener('change', function() {
                // Remove existing previews
                const existingPreviewContainer = document.getElementById('additional-images-preview');
                if (existingPreviewContainer) {
                    existingPreviewContainer.remove();
                }

                if (this.files && this.files.length > 0) {
                    // Create preview container
                    const previewContainer = document.createElement('div');
                    previewContainer.id = 'additional-images-preview';
                    previewContainer.className = 'row mt-2';

                    // Create preview for each file
                    Array.from(this.files).forEach((file, index) => {
                        const reader = new FileReader();
                        reader.onload = function(e) {
                            const colDiv = document.createElement('div');
                            colDiv.className = 'col-md-3 col-sm-4 col-6 mb-2 preview-image-item';
                            colDiv.setAttribute('data-file-index', index);

                            const imgContainer = document.createElement('div');
                            imgContainer.className = 'position-relative';

                            const img = document.createElement('img');
                            img.src = e.target.result;
                            img.className = 'img-thumbnail';
                            img.style.height = '100px';
                            img.style.width = '100%';
                            img.style.objectFit = 'cover';
                            img.alt = `Preview ${index + 1}`;

                            const deleteBtn = document.createElement('button');
                            deleteBtn.type = 'button';
                            deleteBtn.className = 'btn btn-danger btn-sm position-absolute top-0 end-0 rounded-circle delete-preview-btn';
                            deleteBtn.style.cssText = 'width: 25px; height: 25px; padding: 0; font-size: 12px; transform: translate(50%, -50%);';
                            deleteBtn.title = 'حذف الصورة';
                            deleteBtn.innerHTML = '<i class="fas fa-times"></i>';
                            deleteBtn.setAttribute('data-file-index', index);

                            imgContainer.appendChild(img);
                            imgContainer.appendChild(deleteBtn);
                            colDiv.appendChild(imgContainer);
                            previewContainer.appendChild(colDiv);
                        }
                        reader.readAsDataURL(file);
                    });

                    // Add preview container after input
                    additionalImagesInput.parentNode.appendChild(previewContainer);
                }
            });
        }

        // Handle preview image deletion for add news
        document.addEventListener('click', function(e) {
            if (e.target.closest('.delete-preview-btn')) {
                e.preventDefault();
                const button = e.target.closest('.delete-preview-btn');
                const fileIndex = parseInt(button.getAttribute('data-file-index'));
                const imageItem = button.closest('.preview-image-item');

                // Remove the preview image
                if (imageItem) {
                    imageItem.remove();
                }

                // Update the file input to remove the deleted file
                const additionalImagesInput = document.getElementById('{{ form.additional_images.id_for_label }}');
                if (additionalImagesInput && additionalImagesInput.files) {
                    const dt = new DataTransfer();
                    const files = Array.from(additionalImagesInput.files);

                    // Add all files except the deleted one
                    files.forEach((file, index) => {
                        if (index !== fileIndex) {
                            dt.items.add(file);
                        }
                    });

                    // Update the input with the new file list
                    additionalImagesInput.files = dt.files;

                    // Update the data-file-index attributes for remaining items
                    const remainingItems = document.querySelectorAll('.preview-image-item');
                    remainingItems.forEach((item, newIndex) => {
                        item.setAttribute('data-file-index', newIndex);
                        const deleteBtn = item.querySelector('.delete-preview-btn');
                        if (deleteBtn) {
                            deleteBtn.setAttribute('data-file-index', newIndex);
                        }
                    });
                }
            }
        });

        // Handle main image preview deletion for add news
        document.addEventListener('click', function(e) {
            if (e.target.closest('#image-preview-delete')) {
                e.preventDefault();
                const preview = document.getElementById('image-preview');
                const deleteBtn = document.getElementById('image-preview-delete');
                const imageInput = document.getElementById('{{ form.image.id_for_label }}');

                if (preview) preview.remove();
                if (deleteBtn) deleteBtn.remove();
                if (imageInput) imageInput.value = '';
            }
        });

        // Update main image preview to include delete button
        const imageInput = document.getElementById('{{ form.image.id_for_label }}');
        if (imageInput) {
            imageInput.addEventListener('change', function() {
                if (this.files && this.files[0]) {
                    const reader = new FileReader();
                    reader.onload = function(e) {
                        // Remove existing preview and delete button
                        const existingPreview = document.getElementById('image-preview');
                        const existingDeleteBtn = document.getElementById('image-preview-delete');
                        if (existingPreview) existingPreview.remove();
                        if (existingDeleteBtn) existingDeleteBtn.remove();

                        // Create new preview with delete button
                        const previewContainer = document.createElement('div');
                        previewContainer.className = 'position-relative d-inline-block mt-2';

                        const preview = document.createElement('img');
                        preview.id = 'image-preview';
                        preview.className = 'img-fluid border rounded';
                        preview.style.maxHeight = '200px';
                        preview.src = e.target.result;

                        const deleteBtn = document.createElement('button');
                        deleteBtn.type = 'button';
                        deleteBtn.id = 'image-preview-delete';
                        deleteBtn.className = 'btn btn-danger btn-sm position-absolute top-0 end-0 rounded-circle';
                        deleteBtn.style.cssText = 'width: 25px; height: 25px; padding: 0; font-size: 12px; transform: translate(50%, -50%);';
                        deleteBtn.title = 'حذف الصورة';
                        deleteBtn.innerHTML = '<i class="fas fa-times"></i>';

                        previewContainer.appendChild(preview);
                        previewContainer.appendChild(deleteBtn);
                        imageInput.parentNode.appendChild(previewContainer);
                    }
                    reader.readAsDataURL(this.files[0]);
                }
            });
        }
    });
</script>
{% endblock %}
